
:root {
    --font-grotesk: 'Space Grotesk', system-ui, -apple-system, sans-serif;
    --dark-bg: #1a1a1a;
    --dark-secondary: #2d2d2d;
    --dark-text: #e0e0e0;
    --dark-border: #404040;
    --accent-color: #4CAF50;
    --accent-hover: #45a049;
}

body {
    background-color: var(--dark-bg);
    color: var(--dark-text);
}

.container {
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    text-align: center; /* Centrar el contenido */
}

.input-section {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid var(--dark-border);
    border-radius: 5px;
    background-color: var(--dark-secondary);
    text-align: center; /* Centrar los elementos */
}

.preview-section {
    padding: 20px;
    border: 1px solid var(--dark-border);
    border-radius: 5px;
    background-color: var(--dark-secondary);
}

.preview-section h2 {
    color: var(--dark-text);
    margin-bottom: 20px;
    text-align: center; /* Centrar el título */
}

/* El preview mantiene tamaño carta con márgenes correctos */
#preview {
    margin: 0 auto;
    padding: 10mm;          /* Reducido para dar más espacio */
    width: 190mm;          /* Reducido para evitar cortes */
    min-height: 279.4mm;
    background-color: white;
    color: black;
    box-shadow: 0 0 20px rgba(0,0,0,0.3);
    overflow: auto;
    font-family: var(--font-grotesk);
    font-size: 11pt; /* Tamaño base consistente */
    box-sizing: border-box;
}

/* Estilo para los saltos de página explícitos */
#preview div.page-break {
    display: block;
    page-break-after: always;
    break-after: page;
    height: 0;
    padding: 0;
    margin: 0;
    border: none;
    clear: both;
}

/* Estilizar el input file */
input[type="file"] {
    display: inline-block;
    margin: 0 auto;
    background-color: var(--dark-secondary);
    color: var(--dark-text);
    border: 1px solid var(--dark-border);
    padding: 8px;
    border-radius: 4px;
    width: auto;
}

/* Estilizar el botón */
button {
    display: inline-block;
    margin-left: 10px;
    padding: 8px 16px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: var(--font-grotesk);
    vertical-align: middle; /* Alinear verticalmente con el input */
}

button:hover {
    background-color: var(--accent-hover);
}

/* Estilizar la barra de desplazamiento */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--dark-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--dark-border);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* El mensaje de carga */
#loading {
    display: none; /* Oculto por defecto */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--dark-secondary);
    color: var(--dark-text);
    padding: 20px 40px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    z-index: 1000;
}

/* Mantener los estilos del preview como estaban */
#preview h1,
#preview h2,
#preview h3,
#preview h4,
#preview h5,
#preview h6,
#preview p,
#preview ul,
#preview ol,
#preview blockquote,
#preview table {
    /* Los estilos existentes se mantienen igual */
    color: black;
}

/* Estilos para el contenido dentro del preview */
#preview h1 { font-size: 1.6em; }  /* Reducido de 1.8em */
#preview h2 { font-size: 1.4em; }  /* Reducido de 1.5em */
#preview h3 { font-size: 1.2em; }  /* Reducido de 1.3em */
#preview h4 { font-size: 1.1em; }
#preview h5, #preview h6 { font-size: 1em; }

#preview p {
    text-align: justify;
    margin-bottom: 0.8em; /* Reducido de 1em */
    line-height: 1.3;       /* Reducido de 1.4 */
    font-family: var(--font-grotesk);
    font-size: 11pt;        /* Tamaño específico para párrafos */
}

/* Ajuste para párrafos seguidos de títulos */
#preview p + h1,
#preview p + h2,
#preview p + h3,
#preview p + h4,
#preview p + h5,
#preview p + h6 {
    margin-top: 1.2em; /* Reducido de 1.5em */
}

#preview h1:not(:first-child),
#preview h2,
#preview h3,
#preview h4,
#preview h5,
#preview h6 {
    margin-top: 1em;        /* Reducido de 1.5em */
    margin-bottom: 0.4em;   /* Reducido de 0.6em */
    text-align: left;
    font-family: var(--font-grotesk);
    font-weight: 600;
}

/* Estilos para imágenes con error y botón de carga */
#preview img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0.8em auto;
}

#preview img.error {
    display: none;
}

#preview img.error + .image-upload-container {
    display: block;
    width: 100%;
    padding: 20px;
    margin: 0.8em auto;
    text-align: center;
    background-color: var(--dark-secondary);
    border: 2px dashed var(--dark-border);
    border-radius: 8px;
}

.image-upload-container {
    display: none;
}

.image-upload-btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: var(--font-grotesk);
    transition: all 0.3s ease;
}

.image-upload-btn:hover {
    background-color: var(--accent-hover);
}

.image-upload-input {
    display: none;
}

/* Ajustes para impresión */
@media print {
    .image-upload-container {
        display: none !important;
    }
}

#preview h1, #preview h2, #preview h3, #preview h4, #preview h5, #preview h6 {
    margin-top: 20px;
    margin-bottom: 10px;
    text-align: left; /* Los títulos no se justifican */
}

/* Los bloques de código no se justifican */
#preview pre,
#preview code {
    text-align: left;
    font-family: 'Fira Code', monospace;
    border-radius: 6px;
    margin: 0.5em 0;        /* Reducido de 1em */
    width: 100%;
    box-sizing: border-box;
}

#preview pre {
    padding: 1em;
    overflow-x: auto;
    background: #282a36 !important; /* Fondo Dracula */
    white-space: pre-wrap;       /* Permite el wrap del código */
    word-wrap: break-word;       /* Rompe palabras largas */
    word-break: break-all;       /* Asegura que el texto se rompa */
}

#preview code {
    padding: 0.2em 0.4em;
    font-size: 0.85em;          /* Reducimos tamaño de fuente */
    background: #282a36;
    color: #f8f8f2;
    display: block;             /* Asegura que tome el ancho completo */
    width: 100%;
}

#preview pre code {
    padding: 0;
    font-size: 0.8em;       /* Reducido de 0.85em */
    background: transparent;
    white-space: pre-wrap;      /* Permite el wrap del código */
    word-wrap: break-word;
}

/* Ajustes para impresión de código */
@media print {
    #preview pre,
    #preview code {
        background-color: #f8f8f8 !important;
        color: #282a36 !important;
        border: 1px solid #ddd;
    }

    #preview pre code {
        border: none;
    }
}

/* Ajustes específicos para Dracula theme */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: #6272a4;
}

.token.punctuation {
    color: #f8f8f2;
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
    color: #ff79c6;
}

.token.boolean,
.token.number {
    color: #bd93f9;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
    color: #50fa7b;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
    color: #f8f8f2;
}

.token.atrule,
.token.attr-value,
.token.keyword {
    color: #ff79c6;
}

.token.function,
.token.class-name {
    color: #50fa7b;
}

.token.regex,
.token.important,
.token.variable {
    color: #f1fa8c;
}

#preview ul,
#preview ol {
    text-align: left; /* Las listas no se justifican */
    padding-left: 20px;
    font-family: var(--font-grotesk);
    margin-top: 0.3em;
    margin-bottom: 0.3em;
    font-size: 11pt; /* Mismo tamaño que los párrafos */
    line-height: 1.3; /* Consistente con párrafos */
}

#preview blockquote {
    text-align: justify;
    border-left: 4px solid #ccc;
    padding-left: 20px;
    margin: 0.8em 0;        /* Reducido de 20px */
    color: #666;
    font-family: var(--font-grotesk);
}

#preview table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.8em 0;        /* Reducido de 20px */
    font-family: var(--font-grotesk);
}

#preview th,
#preview td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left; /* Las celdas de tabla no se justifican */
}

/* Asegurar que la fuente se mantenga en la impresión */
@media print {
    #preview {
        font-family: var(--font-grotesk);
    }
}

/* Ajustes de impresión unificados */
@media print {
    body {
        margin: 0;
        padding: 0;
        background: none;
    }

    #preview {
        width: 100% !important;
        padding: 10mm !important;
        margin: 0 !important;
        box-shadow: none;
        overflow: visible;
        font-size: 11pt;
        min-height: auto !important;
    }

    /* Evitar saltos de página dentro de elementos importantes */
    #preview h1, #preview h2, #preview h3,
    #preview img, #preview pre, #preview table {
        page-break-inside: avoid !important;
    }

    /* Evitar que los títulos queden solos al final de una página */
    #preview h1, #preview h2, #preview h3 {
        page-break-after: avoid !important;
    }

    /* Eliminar elementos que no deben aparecer en la impresión */
    .image-upload-container,
    #preview div:empty,
    #preview p:empty {
        display: none !important;
    }

    /* Asegurar que los saltos de página explícitos funcionen correctamente */
    #preview div.page-break {
        display: block !important;
        page-break-after: always !important;
        break-after: page !important;
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
    }

    /* Ajustes de impresión para evitar cortes de frases */
    /* Evitar cortes de frases en párrafos y código */
    #preview p, 
    #preview li,
    #preview pre, 
    #preview code {
        page-break-inside: auto !important;
        orphans: 3 !important; /* Mínimo 3 líneas al final de página */
        widows: 3 !important;  /* Mínimo 3 líneas al inicio de página */
    }
    
    /* Evitar que los títulos queden solos al final de una página */
    #preview h1, #preview h2, #preview h3, #preview h4, #preview h5, #preview h6 {
        page-break-after: avoid !important;
        page-break-inside: avoid !important;
    }
}

/* Ajustar el contenido de código */
#preview pre,
#preview code {
    max-width: 100%;
    box-sizing: border-box;
    margin: 0.5em 0; /* Reducir margen */
    overflow-x: hidden;
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* Items de lista */
#preview ul li,
#preview ol li {
    font-size: 11pt; /* Asegurar mismo tamaño en items */
    margin-bottom: 0.3em;
}

/* Mantener el tamaño reducido solo para código */
#preview pre,
#preview code {
    text-align: left;
    font-family: 'Fira Code', monospace;
    border-radius: 6px;
    margin: 0.5em 0;
    width: 100%;
    box-sizing: border-box;
    font-size: 0.85em; /* Mantener tamaño reducido solo para código */
}

/* Asegurar consistencia en impresión - Reglas unificadas */
@media print {
    /* Tamaños de fuente consistentes */
    #preview,
    #preview p,
    #preview ul,
    #preview ol,
    #preview ul li,
    #preview ol li {
        font-size: 11pt !important;
    }

    #preview pre,
    #preview code {
        font-size: 0.85em !important;
    }

    /* Optimizar espaciado */
    #preview p,
    #preview ul,
    #preview ol {
        margin-top: 0.3em !important;
        margin-bottom: 0.3em !important;
    }
}

/* Estilo para texto con comillas invertidas (inline code) */
#preview p code,
#preview li code,
#preview td code {
    display: inline;
    padding: 0.2em 0.4em;
    font-size: 0.85em;
    background: #282a36;
    color: #f8f8f2;
    border-radius: 3px;
    font-family: 'Fira Code', monospace;
    width: auto;
    white-space: normal;
}

/* Mantener el estilo de bloques de código (pre code) como está */
#preview pre code {
    display: block;
    width: 100%;
    white-space: pre-wrap;
}

/* Ajustes para impresión */
@media print {
    #preview p code,
    #preview li code,
    #preview td code {
        background-color: #f8f8f8 !important;
        color: #282a36 !important;
        border: 1px solid #ddd;
        display: inline;
        width: auto;
    }
}

