
// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    // Obtener elementos del DOM
    const preview = document.getElementById('preview');
    const fileInput = document.getElementById('mdFile');

    // Verificar que los elementos existen
    if (!preview || !fileInput) {
        console.error('No se encontraron elementos necesarios en el DOM');
        return;
    }

    // Verificar que Prism está disponible
    if (typeof Prism === 'undefined') {
        console.error('Prism no está disponible');
        return;
    }

    // Configurar Prism autoloader si está disponible
    if (Prism.plugins && Prism.plugins.autoloader) {
        Prism.plugins.autoloader.languages_path = 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/';
    }

    // Configurar marked para mejorar el renderizado
    marked.setOptions({
        breaks: true, // Permite saltos de línea con un solo retorno
        gfm: true,    // GitHub Flavored Markdown
        sanitize: false, // Permite HTML en el markdown
        highlight: function(code, lang) {
            if (!lang) return code;

            const languageMap = {
                'js': 'javascript',
                'py': 'python',
                'cs': 'csharp',
                'cpp': 'cpp',
                'ts': 'typescript',
                'yml': 'yaml',
                'shell': 'bash',
                'sh': 'bash',
                'html': 'markup',
                'xml': 'markup',
                'bat': 'batch',
                'cmd': 'batch'
            };

            const normalizedLang = languageMap[lang] || lang;

            try {
                return `<pre class="language-${normalizedLang}"><code class="language-${normalizedLang}">${code}</code></pre>`;
            } catch (e) {
                console.warn('Error highlighting code:', e);
                return `<pre><code>${code}</code></pre>`;
            }
        }
    });

    // Agregar extensión personalizada para marked
    const pageBreakExtension = {
        name: 'pageBreak',
        level: 'block',
        start(src) { return src.match(/^\[SALTO_PAGINA\]/)?.index; },
        tokenizer(src) {
            const rule = /^\[SALTO_PAGINA\]\n?/;
            const match = rule.exec(src);
            if (match) {
                return {
                    type: 'pageBreak',
                    raw: match[0],
                };
            }
        },
        renderer() {
            return '<div class="page-break"></div>';
        }
    };

    // Registrar la extensión
    marked.use({ extensions: [pageBreakExtension] });

    // Escuchar cambios en el input file
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                preview.innerHTML = marked.parse(content);

                // Agregar manejador de error a todas las imágenes
                preview.querySelectorAll('img').forEach(img => {
                    img.addEventListener('error', () => handleImageError(img));
                });

                // Verificar que Prism existe antes de llamar a highlightAll
                if (typeof Prism !== 'undefined' && typeof Prism.highlightAll === 'function') {
                    // Usar setTimeout para asegurar que el DOM se ha actualizado
                    setTimeout(() => {
                        Prism.highlightAll();
                    }, 0);
                }
            };
            reader.readAsText(file);
        }
    });
});

// Función para verificar si un elemento está vacío o solo contiene espacios en blanco
function isElementEmpty(element) {
    // Obtener solo el texto, eliminando espacios al inicio y final
    const text = element.textContent.trim();
    // Verificar si hay contenido visible
    const hasVisibleContent = element.querySelector('img, svg, canvas, video, iframe');
    return text === '' && !hasVisibleContent;
}

// Función para exportar a PDF
window.exportToPDF = function() {
    const element = document.getElementById('preview');
    const loadingElement = document.getElementById('loading') || createLoadingElement();

    if (!element) {
        console.error('Elemento preview no encontrado');
        return;
    }

    // Eliminar elementos vacíos que podrían causar páginas en blanco
    removeEmptyElements(element);

    const opt = {
        margin: [15, 10, 20, 10], // Reducir márgenes para optimizar espacio
        filename: 'document.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
            scale: 2,
            useCORS: true,
            letterRendering: true
        },
        jsPDF: {
            unit: 'mm',
            format: 'letter',
            orientation: 'portrait'
        },
        pagebreak: {
            mode: ['css', 'legacy', 'avoid-all'], // Añadido 'avoid-all' para evitar cortes
            // Elementos que se consideran para saltos de página
            cssEl: '.page-break',
            // Elementos que se mantendrán con su contenido siguiente
            avoid: [
                'img',
                'pre',
                'table',
                'p',           // Añadido para evitar cortar párrafos
                'li',          // Añadido para evitar cortar elementos de lista
                'code'         // Añadido para evitar cortar bloques de código
            ]
        },
        footer: {
            height: '20px', // Reducir altura del footer
            contents: {
                default: '<span style="color: #444; font-size: 10px; font-family: Space Grotesk; margin-right: 10mm; float: right;">{{page}}</span>'
            }
        }
    };

    // Mostrar mensaje de carga
    loadingElement.style.display = 'block';

    // Crear una copia del elemento para manipularlo sin afectar la vista
    const clonedElement = element.cloneNode(true);

    // Asegurar que no haya saltos de página innecesarios
    optimizeForPrinting(clonedElement);

    // Eliminar cualquier elemento vacío que pueda causar páginas en blanco
    // pero preservar los saltos de página explícitos
    const emptyDivs = clonedElement.querySelectorAll('div:empty:not(.page-break), p:empty');
    emptyDivs.forEach(div => div.remove());

    // Asegurar que los saltos de página explícitos tengan el atributo correcto
    const pageBreaks = clonedElement.querySelectorAll('.page-break');
    pageBreaks.forEach(pb => {
        pb.setAttribute('data-html2pdf-pagebreak', '');
    });

    html2pdf()
        .set(opt)
        .from(clonedElement)
        .save()
        .then(() => {
            setTimeout(() => {
                loadingElement.style.display = 'none';
            }, 500);
        })
        .catch(error => {
            console.error('Error al generar PDF:', error);
            loadingElement.style.display = 'none';
        });
};

// Función auxiliar para crear el elemento de carga
function createLoadingElement() {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loading';
    loadingDiv.textContent = 'Generando PDF...';
    document.body.appendChild(loadingDiv);
    return loadingDiv;
}

// Función para manejar errores de imágenes
function handleImageError(img) {
    // Marcar la imagen como error
    img.classList.add('error');

    // Crear contenedor para el botón de carga
    const container = document.createElement('div');
    container.className = 'image-upload-container';

    // Crear input file oculto
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.className = 'image-upload-input';

    // Crear botón visible
    const button = document.createElement('button');
    button.className = 'image-upload-btn';
    button.textContent = 'Cargar imagen';

    // Agregar elementos al DOM
    container.appendChild(input);
    container.appendChild(button);
    img.parentNode.insertBefore(container, img.nextSibling);

    // Manejar clic en el botón
    button.addEventListener('click', () => input.click());

    // Manejar cambio en el input file
    input.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                img.src = e.target.result;
                img.classList.remove('error');
                container.remove();
            };
            reader.readAsDataURL(file);
        }
    });
}

// Función para eliminar elementos vacíos que podrían causar páginas en blanco
function removeEmptyElements(element) {
    // Buscar todos los divs vacíos
    const emptyDivs = element.querySelectorAll('div:empty, p:empty');
    emptyDivs.forEach(div => div.remove());

    // Buscar elementos <br> consecutivos y eliminar los extras
    const brs = element.querySelectorAll('br + br');
    brs.forEach(br => br.remove());
}

// Función para optimizar el contenido para impresión
function optimizeForPrinting(element) {
    // Asegurar que los saltos de página explícitos funcionen correctamente
    const pageBreaks = element.querySelectorAll('div.page-break');
    pageBreaks.forEach(pb => {
        // Asegurar que el div de salto de página tenga las propiedades correctas
        pb.style.display = 'block';
        pb.style.pageBreakAfter = 'always';
        pb.style.breakAfter = 'page';
        pb.style.height = '0';
        pb.style.padding = '0';
        pb.style.margin = '0';
        pb.style.border = 'none';
        pb.style.clear = 'both';

        // Asegurar que el salto de página sea reconocido por html2pdf
        pb.setAttribute('data-html2pdf-pagebreak', '');

        // Si hay contenido vacío después del salto de página, eliminarlo
        let nextElement = pb.nextElementSibling;
        if (nextElement && isElementEmpty(nextElement)) {
            nextElement.remove();
        }
    });

    // Agregar saltos de página antes de cada encabezado H1
    // Excepto el primer H1 si está al inicio del documento
    const h1Headers = element.querySelectorAll('h1');
    h1Headers.forEach((h1, index) => {
        // No agregar salto de página antes del primer H1 si es el primer elemento o está muy cerca del inicio
        if (index === 0) {
            // Verificar si el H1 está al inicio del documento
            let isAtBeginning = true;
            let prevElement = h1.previousElementSibling;
            let elementCount = 0;

            // Contar cuántos elementos hay antes del primer H1
            while (prevElement && elementCount < 3) {
                // Si hay contenido significativo antes del H1, no está al inicio
                if (!isElementEmpty(prevElement) && !prevElement.classList.contains('page-break')) {
                    isAtBeginning = false;
                    break;
                }
                prevElement = prevElement.previousElementSibling;
                elementCount++;
            }

            // Si el primer H1 no está al inicio, agregar salto de página
            if (!isAtBeginning) {
                insertPageBreakBefore(h1);
            }
        } else {
            // Para todos los demás H1, siempre agregar salto de página
            insertPageBreakBefore(h1);
        }
    });

    // Eliminar elementos vacíos excepto los saltos de página
    const emptyElements = element.querySelectorAll('div:not(.page-break), p, span');
    emptyElements.forEach(el => {
        if (isElementEmpty(el) && !el.classList.contains('page-break')) {
            el.remove();
        }
    });
}

// Función auxiliar para insertar un salto de página antes de un elemento
function insertPageBreakBefore(element) {
    // Crear el elemento de salto de página
    const pageBreak = document.createElement('div');
    pageBreak.className = 'page-break';
    pageBreak.style.display = 'block';
    pageBreak.style.pageBreakAfter = 'always';
    pageBreak.style.breakAfter = 'page';
    pageBreak.style.height = '0';
    pageBreak.style.padding = '0';
    pageBreak.style.margin = '0';
    pageBreak.style.border = 'none';
    pageBreak.style.clear = 'both';

    // Asegurar que el salto de página sea reconocido por html2pdf
    pageBreak.setAttribute('data-html2pdf-pagebreak', '');

    // Insertar el salto de página antes del elemento
    element.parentNode.insertBefore(pageBreak, element);
}

